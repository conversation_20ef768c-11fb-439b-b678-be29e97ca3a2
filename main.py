#!/usr/bin/env python3
"""
RAW Photo Repair Tool - Main Entry Point
Command line interface for the RAW photo repair tool
"""
import argparse
import sys
import os
from pathlib import Path

try:
    from raw_repair_tool import RAWRepairTool
    import utils
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure all dependencies are installed:")
    print("pip install rawpy Pillow numpy exifread tifffile opencv-python colorama tqdm")
    sys.exit(1)


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description="RAW Photo Repair Tool - Detect and repair corrupted RAW image files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --input test --output repaired
  python main.py --input test/DSC_4697.NEF --output repaired --verbose
  python main.py --check test
        """
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='Input file or directory containing RAW files'
    )

    parser.add_argument(
        '--output', '-o',
        default='repaired',
        help='Output directory for repaired files (default: repaired)'
    )

    parser.add_argument(
        '--check', '-c',
        action='store_true',
        help='Only check file integrity, do not attempt repairs'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )

    parser.add_argument(
        '--backup',
        action='store_true',
        default=True,
        help='Create backup of original files (default: True)'
    )

    args = parser.parse_args()

    # Validate input path
    if not os.path.exists(args.input):
        print(f"Error: Input path '{args.input}' does not exist")
        sys.exit(1)

    # Initialize repair tool
    repair_tool = RAWRepairTool(verbose=args.verbose)

    try:
        if os.path.isfile(args.input):
            # Process single file
            if not utils.is_raw_file(args.input):
                print(f"Error: '{args.input}' is not a supported RAW file")
                sys.exit(1)

            print(f"Processing single file: {args.input}")

            # Check integrity
            integrity = repair_tool.check_file_integrity(args.input)

            if args.check:
                # Only check, don't repair
                if integrity['is_valid']:
                    print(f"✓ File is valid: {args.input}")
                else:
                    print(f"✗ File is corrupted: {args.input}")
                    print("Errors found:")
                    for error in integrity['errors']:
                        print(f"  - {error}")
            else:
                # Check and repair if needed
                if not integrity['is_valid']:
                    print(f"Corrupted file detected: {args.input}")
                    success = repair_tool.attempt_repair(args.input, args.output)
                    if success:
                        print(f"✓ File successfully repaired")
                    else:
                        print(f"✗ Failed to repair file")
                        sys.exit(1)
                else:
                    print(f"✓ File is already valid: {args.input}")

        elif os.path.isdir(args.input):
            # Process directory
            print(f"Processing directory: {args.input}")

            if args.check:
                # Only check files in directory
                raw_files = utils.find_raw_files(args.input)
                if not raw_files:
                    print("No RAW files found in directory")
                    sys.exit(0)

                print(f"Checking {len(raw_files)} RAW files...")
                corrupted_count = 0

                for file_path in raw_files:
                    integrity = repair_tool.check_file_integrity(file_path)
                    if integrity['is_valid']:
                        print(f"✓ {file_path}")
                    else:
                        print(f"✗ {file_path}")
                        corrupted_count += 1
                        if args.verbose:
                            for error in integrity['errors']:
                                print(f"    - {error}")

                print(f"\nSummary: {corrupted_count} corrupted files found out of {len(raw_files)} total")

            else:
                # Process directory with repairs
                stats = repair_tool.process_directory(args.input, args.output)
                repair_tool.print_summary()

        else:
            print(f"Error: '{args.input}' is neither a file nor a directory")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()

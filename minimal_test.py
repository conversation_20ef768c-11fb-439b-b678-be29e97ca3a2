import sys
print("Python is working", file=sys.stderr)
print("Arguments:", sys.argv, file=sys.stderr)

try:
    import rawpy
    print("rawpy imported", file=sys.stderr)
    
    # Try to process the NEF file
    with rawpy.imread("test/DSC_4697.NEF") as raw:
        print("File opened", file=sys.stderr)
        rgb = raw.postprocess(use_camera_wb=True, half_size=True)
        print("Processing done", file=sys.stderr)
        
        from PIL import Image
        import os
        os.makedirs("output", exist_ok=True)
        Image.fromarray(rgb).save("output/test.tiff")
        print("File saved", file=sys.stderr)
        
except Exception as e:
    print(f"Error: {e}", file=sys.stderr)
    import traceback
    traceback.print_exc(file=sys.stderr)

print("Script completed", file=sys.stderr)

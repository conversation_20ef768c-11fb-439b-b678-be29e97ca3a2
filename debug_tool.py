#!/usr/bin/env python3
"""
Debug version of RAW repair tool
"""

def main():
    print("=== RAW Repair Tool Debug ===")
    
    # Test basic Python functionality
    print("1. Testing basic Python...")
    import os
    import sys
    print(f"   Python version: {sys.version}")
    print(f"   Current directory: {os.getcwd()}")
    print(f"   Arguments: {sys.argv}")
    
    # Test file access
    print("\n2. Testing file access...")
    test_file = "test/DSC_4697.NEF"
    if os.path.exists(test_file):
        size = os.path.getsize(test_file)
        print(f"   ✓ File exists: {test_file} ({size:,} bytes)")
    else:
        print(f"   ✗ File not found: {test_file}")
        return
    
    # Test imports one by one
    print("\n3. Testing imports...")
    
    try:
        import numpy as np
        print("   ✓ numpy imported")
    except ImportError as e:
        print(f"   ✗ numpy failed: {e}")
        return
    
    try:
        from PIL import Image
        print("   ✓ PIL imported")
    except ImportError as e:
        print(f"   ✗ PIL failed: {e}")
        return
    
    try:
        import rawpy
        print("   ✓ rawpy imported")
    except ImportError as e:
        print(f"   ✗ rawpy failed: {e}")
        return
    
    # Test RAW file reading
    print("\n4. Testing RAW file reading...")
    try:
        with rawpy.imread(test_file) as raw:
            print(f"   ✓ RAW file opened successfully")
            print(f"   Image size: {raw.sizes.raw_width} x {raw.sizes.raw_height}")
            
            # Test processing
            print("   Testing basic processing...")
            rgb = raw.postprocess(use_camera_wb=True, half_size=True)
            print(f"   ✓ Processing successful, shape: {rgb.shape}")
            
            # Test saving
            print("   Testing save...")
            output_dir = "debug_output"
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, "test_output.tiff")
            
            Image.fromarray(rgb).save(output_file)
            print(f"   ✓ File saved: {output_file}")
            
            # Verify saved file
            if os.path.exists(output_file):
                saved_size = os.path.getsize(output_file)
                print(f"   ✓ Saved file verified ({saved_size:,} bytes)")
            
    except Exception as e:
        print(f"   ✗ RAW processing failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n✓ All tests passed! The tool should work correctly.")
    print("\nTo use the full tool:")
    print("  python main.py --input test/DSC_4697.NEF --output repaired")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\nFatal error: {e}")
        import traceback
        traceback.print_exc()

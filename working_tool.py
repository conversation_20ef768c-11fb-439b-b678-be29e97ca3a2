#!/usr/bin/env python3
"""
Working RAW repair tool - simplified version
"""
import os
import sys

def main():
    # Force output to be visible
    sys.stdout.write("=== RAW Photo Repair Tool ===\n")
    sys.stdout.flush()
    
    # Get input file from command line
    if len(sys.argv) < 2:
        sys.stdout.write("Usage: python working_tool.py <input_file>\n")
        sys.stdout.write("Example: python working_tool.py test/DSC_4697.NEF\n")
        sys.stdout.flush()
        return
    
    input_file = sys.argv[1]
    sys.stdout.write(f"Processing: {input_file}\n")
    sys.stdout.flush()
    
    # Check if file exists
    if not os.path.exists(input_file):
        sys.stdout.write(f"Error: File '{input_file}' not found\n")
        sys.stdout.flush()
        return
    
    # Get file info
    size = os.path.getsize(input_file)
    sys.stdout.write(f"File size: {size:,} bytes\n")
    sys.stdout.flush()
    
    # Check if it's a RAW file
    _, ext = os.path.splitext(input_file)
    if ext.lower() not in ['.nef', '.cr2', '.cr3', '.arw', '.dng']:
        sys.stdout.write(f"Warning: '{ext}' may not be a supported RAW format\n")
        sys.stdout.flush()
    
    # Try to process the file
    try:
        sys.stdout.write("Importing rawpy...\n")
        sys.stdout.flush()
        import rawpy
        
        sys.stdout.write("Opening RAW file...\n")
        sys.stdout.flush()
        with rawpy.imread(input_file) as raw:
            sys.stdout.write(f"✓ RAW file opened successfully\n")
            sys.stdout.write(f"Image size: {raw.sizes.raw_width} x {raw.sizes.raw_height}\n")
            sys.stdout.flush()
            
            sys.stdout.write("Processing RAW data...\n")
            sys.stdout.flush()
            rgb = raw.postprocess(
                use_camera_wb=True,
                half_size=False,
                no_auto_bright=True,
                output_bps=16
            )
            
            sys.stdout.write(f"✓ Processing complete, output shape: {rgb.shape}\n")
            sys.stdout.flush()
            
            # Save the result
            sys.stdout.write("Importing PIL...\n")
            sys.stdout.flush()
            from PIL import Image
            
            # Create output directory
            output_dir = "repaired"
            os.makedirs(output_dir, exist_ok=True)
            
            # Generate output filename
            filename = os.path.basename(input_file)
            name, _ = os.path.splitext(filename)
            output_file = os.path.join(output_dir, f"{name}_repaired.tiff")
            
            sys.stdout.write(f"Saving to: {output_file}\n")
            sys.stdout.flush()
            
            Image.fromarray(rgb).save(output_file)
            
            # Verify the saved file
            if os.path.exists(output_file):
                saved_size = os.path.getsize(output_file)
                sys.stdout.write(f"✓ File saved successfully ({saved_size:,} bytes)\n")
                sys.stdout.flush()
            else:
                sys.stdout.write("✗ Error: Output file was not created\n")
                sys.stdout.flush()
                return
            
    except ImportError as e:
        sys.stdout.write(f"✗ Import error: {e}\n")
        sys.stdout.write("Please install required packages:\n")
        sys.stdout.write("pip install rawpy Pillow\n")
        sys.stdout.flush()
        return
        
    except Exception as e:
        sys.stdout.write(f"✗ Processing error: {e}\n")
        sys.stdout.flush()
        return
    
    sys.stdout.write("\n✓ RAW file processing completed successfully!\n")
    sys.stdout.write(f"Repaired file saved to: {output_file}\n")
    sys.stdout.flush()

if __name__ == "__main__":
    main()

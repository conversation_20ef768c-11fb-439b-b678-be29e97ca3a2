#!/usr/bin/env python3
"""
Simplified RAW repair tool for testing
"""
import os
import sys

def main():
    print("=== RAW Photo Repair Tool - Test Version ===")
    
    # Check command line arguments
    if len(sys.argv) < 3:
        print("Usage: python test_tool.py --input <file_or_dir>")
        return
    
    input_path = None
    for i, arg in enumerate(sys.argv):
        if arg == "--input" and i + 1 < len(sys.argv):
            input_path = sys.argv[i + 1]
            break
    
    if not input_path:
        print("Error: No input path specified")
        return
    
    print(f"Input path: {input_path}")
    
    # Check if path exists
    if not os.path.exists(input_path):
        print(f"Error: Path '{input_path}' does not exist")
        return
    
    if os.path.isfile(input_path):
        print(f"Processing single file: {input_path}")
        process_file(input_path)
    elif os.path.isdir(input_path):
        print(f"Processing directory: {input_path}")
        process_directory(input_path)
    else:
        print(f"Error: '{input_path}' is neither a file nor directory")

def process_file(file_path):
    """Process a single file"""
    print(f"  File: {file_path}")
    
    # Check file size
    try:
        size = os.path.getsize(file_path)
        print(f"  Size: {size:,} bytes")
    except Exception as e:
        print(f"  Error getting file size: {e}")
        return
    
    # Check file extension
    _, ext = os.path.splitext(file_path)
    print(f"  Extension: {ext}")
    
    if ext.lower() in ['.nef', '.cr2', '.cr3', '.arw', '.dng']:
        print("  ✓ RAW file detected")
        try_repair(file_path)
    else:
        print("  ✗ Not a supported RAW file")

def process_directory(dir_path):
    """Process all files in a directory"""
    try:
        files = os.listdir(dir_path)
        print(f"  Found {len(files)} files")
        
        raw_files = []
        for file in files:
            file_path = os.path.join(dir_path, file)
            if os.path.isfile(file_path):
                _, ext = os.path.splitext(file)
                if ext.lower() in ['.nef', '.cr2', '.cr3', '.arw', '.dng']:
                    raw_files.append(file_path)
        
        print(f"  Found {len(raw_files)} RAW files")
        
        for raw_file in raw_files:
            process_file(raw_file)
            
    except Exception as e:
        print(f"  Error processing directory: {e}")

def try_repair(file_path):
    """Try to repair a RAW file"""
    print(f"  Attempting repair...")
    
    try:
        # Try to import rawpy
        import rawpy
        print("  ✓ rawpy library available")
        
        # Try to read the file
        with rawpy.imread(file_path) as raw:
            print("  ✓ File can be read by rawpy")
            print(f"    Image size: {raw.sizes.raw_width} x {raw.sizes.raw_height}")
            
            # Try basic processing
            rgb = raw.postprocess(use_camera_wb=True, half_size=True)
            print("  ✓ Basic processing successful")
            print(f"    Processed shape: {rgb.shape}")
            
            # Try to save
            from PIL import Image
            output_dir = "repaired"
            os.makedirs(output_dir, exist_ok=True)
            
            filename = os.path.basename(file_path)
            name, _ = os.path.splitext(filename)
            output_path = os.path.join(output_dir, f"{name}_repaired.tiff")
            
            Image.fromarray(rgb).save(output_path)
            print(f"  ✓ Saved repaired image: {output_path}")
            
    except ImportError as e:
        print(f"  ✗ Missing library: {e}")
    except Exception as e:
        print(f"  ✗ Repair failed: {e}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Fatal error: {e}")
        import traceback
        traceback.print_exc()
